<script>
import { Button } from 'ant-design-vue'
import _ from 'lodash'
import EventFilter from '../event/components/filter/Filter.vue'
import FilterModelUtil from '../event/models/FilterModelUtil.js'
import CPNTFilter from '../filter/Filter.vue'
import Label from '../label/Filter.vue'
import Segment from '../segment/Filter.vue'
import ActionCollectiveGroup from './ActionCollectiveGroup.vue'
import HandleGroup from './handlegroup.vue'

export default {
  name: 'ActionCollective',
  components: {
    'a-button': Button,
    ActionCollectiveGroup,
    CPNTFilter,
    Label,
    EventFilter,
    HandleGroup,
    Segment,
  },
  provide() {
    return {
      // 提供完整的 ActionCollection 数据结构给子组件
      actionCollectionData: () => this.value || {},
      // 提供 isActionCollection 标识
      isActionCollectionComponent: () => this.isActionCollection,
      // 提供 ActionCollection 的 context
      actionCollectionContext: () => ({
        value: this.value,
        setActionCollectionPushDataExclusive: this.setActionCollectionPushDataExclusive,
      }),
    }
  },
  props: {
    dataProvider: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    mode: {
      type: String,
      default: 'edit',
    },
    value: {
      type: Object,
      default: () => ({}),
    },
    showInitLine: {
      type: Boolean,
      default: false,
    },
    isActionCollection: {
      type: Boolean,
      default: false,
    },
    isUserGroup: {
      type: Boolean,
      default: false,
    },
    showPushData: {
      type: Boolean,
      default: false,
    },
    externalFirstAction: {
      type: [String, Object],
      default: null,
    },
  },
  data() {
    return {
      refObj: {},
      _,
    }
  },
  watch: {
    value: {
      handler(newValue) {
        if (_.isEmpty(newValue)) {
          if (this.showInitLine) {
            this.onChange({
              connector: 'AND',
              filters: [
                {
                  connector: 'AND',
                  eventGroup: {},
                },
              ],
            })
          }
          else {
            this.onChange({
              connector: 'AND',
              filters: [],
            })
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 设置ref引用
    setRef(index, key, el) {
      if (!this.refObj[index]) {
        this.refObj[index] = {}
      }
      this.refObj[index][key] = el
    },

    // 验证方法
    isValid() {
      const resultArr = []
      _.forEach(this.refObj, (v) => {
        const values = _.values(v)
        _.forEach(_.without(values, null), (item) => {
          if (item && item.isValid) {
            resultArr.push(item.isValid(true))
          }
        })
      })
      return !resultArr.includes(false)
    },

    onChangeConnector(datas, type) {
      const result = { ...this.value, [type]: datas }
      this.onChange(result)
    },

    onSubConnectorChange(datas, type, data) {
      data[type] = datas
      this.onChange({ ...this.value })
    },

    count() {
      return (this.value && this.value.filters && this.value.filters.length) || 1
    },

    handleAddSubItem(type, index) {
      const _value = _.cloneDeep(this.value)
      _value.filters[index][type] = {}
      this.onChange(_value)
    },

    onAddItemFilter(type) {
      const _value = _.cloneDeep(this.value)
      _value.filters.push({
        connector: 'AND',
        [type]: {},
      })
      this.onChange(_value)
    },

    onChangeItemFilter(type, v, index, innerValue) {
      const _value = _.cloneDeep(this.value)
      if (_.isEmpty(innerValue.filters)) {
        _value.filters[index] = _.omit(_value.filters[index], type)
        if (
          !_value.filters[index].eventGroup
          && !_value.filters[index].userProperty
          && !_value.filters[index].userLabel
          && !_value.filters[index].segment
        ) {
          _value.filters.splice(index, 1)
        }
      }
      else {
        _value.filters[index][type] = v
        // console.log(JSON.parse(JSON.stringify(v)), 'onChangeItemFilter')
        // console.log(JSON.parse(JSON.stringify(innerValue)), 'onChangeItemFilter')
      }
      // 传递两个参数：validJson 和 fullValue
      this.onChange && this.onChange(_value)
    },

    /**
     * ActionCollection 的 pushData 管理方法
     */
    setActionCollectionPushDataExclusive(targetFilter) {
      console.warn('ActionCollection setActionCollectionPushDataExclusive 调用:', { targetFilter, value: this.value })

      FilterModelUtil.setActionCollectionPushDataExclusive(this.value, targetFilter)

      this.onChange && this.onChange(this.value, this.value)
    },

    getItemKeys(item) {
      return _.keys(item).slice(1)
    },

    getOrderedKeys(item) {
      const keys = this.getItemKeys(item)
      // 整理顺序
      const _keys = []
      _keys[0] = _.find(keys, key => key === 'eventGroup')
      _keys[1] = _.find(keys, key => key === 'userProperty')
      _keys[2] = _.find(keys, key => key === 'userLabel')
      _keys[3] = _.find(keys, key => key === 'segment')

      return _.without(_keys, false, undefined, null)
    },
  },
}
</script>

<template>
  <div class="wolf-static-component_filter_FilterGroupPanel_action_collective">
    <ActionCollectiveGroup
      v-if="!(!showInitLine && _.isEmpty(value?.filters))"
      :connector="value.connector"
      :on-change-connector="(res) => onChangeConnector(res, 'connector')"
      :filter-count="count()"
      inner="inner"
      :mode="mode"
    >
      <template v-for="(item, index) in value.filters">
        <ActionCollectiveGroup
          :key="index"
          :connector="item.connector"
          :on-change-connector="(res) => onSubConnectorChange(res, 'connector', item)"
          :filter-count="getItemKeys(item).length"
          inner="inner"
          :mode="mode"
        >
          <div>
            <template v-for="key in getOrderedKeys(item)">
              <!-- 事件组 -->
              <EventFilter
                v-if="key === 'eventGroup'"
                :ref="(el) => setRef(index, key, el)"
                :key="key"
                :data-provider="dataProvider"
                :show-init-line="true"
                :value="item[key] || {}"
                :on-change="(v, innerValue) => onChangeItemFilter('eventGroup', v, index, innerValue)"
                :mode="mode"
                :is-action-collection="isActionCollection"
                :show-push-data="showPushData"
                :external-first-action="externalFirstAction"
                type="actioncollective"
              />

              <!-- 用户属性 -->
              <CPNTFilter
                v-else-if="key === 'userProperty'"
                :ref="(el) => setRef(index, key, el)"
                :key="key"
                add-button-text="属性"
                :data-provider="dataProvider"
                :value="item[key] || {}"
                :on-change="(v, innerValue) => onChangeItemFilter('userProperty', v, index, innerValue)"
                :mode="mode"
                :is-user-group="isUserGroup"
              />

              <!-- 用户标签 -->
              <Label
                v-else-if="key === 'userLabel'"
                :ref="(el) => setRef(index, key, el)"
                :key="key"
                :data-provider="dataProvider"
                :value="item[key] || {}"
                :on-change="(v, innerValue) => onChangeItemFilter('userLabel', v, index, innerValue)"
                :mode="mode"
                :is-user-group="isUserGroup"
              />

              <!-- 分群 -->
              <Segment
                v-else-if="key === 'segment'"
                :ref="(el) => setRef(index, key, el)"
                :key="key"
                :data-provider="dataProvider"
                :value="item[key] || {}"
                :on-change="(v, innerValue) => onChangeItemFilter('segment', v, index, innerValue)"
                :mode="mode"
              />
            </template>

            <HandleGroup :value="item" :index="index" :on-click="(type) => handleAddSubItem(type, index)" :mode="mode" />
          </div>
        </ActionCollectiveGroup>
      </template>
    </ActionCollectiveGroup>

    <div class="action_collective_button_group" :style="{ display: mode === 'detail' ? 'none' : 'block' }">
      <a-button type="dashed" @click="() => onAddItemFilter('eventGroup')">
        <a-icon type="plus" />
        实时行为
      </a-button>
      <!-- <a-button type="dashed" @click="() => onAddItemFilter('userProperty')">
        <a-icon type="plus" />
        属性
      </a-button> -->
      <a-button type="dashed" @click="() => onAddItemFilter('userLabel')">
        <a-icon type="plus" />
        标签
      </a-button>
      <a-button type="dashed" @click="() => onAddItemFilter('segment')">
        <a-icon type="plus" />
        AI决策模型
      </a-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import "./filter.scss";
</style>
