<script>
import _ from 'lodash'
import { EVENT_CONSTANTS, FilterModelUtil, generateEventTimeDisplayText, getFunValue } from 'wolf-static-cpnt/event'
import { FilterConfig as FILTER_CONFIG } from 'wolf-static-cpnt/event/config'
import { validationMixin } from 'wolf-static-cpnt/event/mixins'
import BaseFilter from '../../../filter/Filter.vue'
import FilterEventAction from '../fields/FilterEventAction.vue'
import FilterEventFieldSelect from '../fields/FilterEventFieldSelect.vue'
import FilterEventFunction from '../fields/FilterEventFunction.vue'
import FilterEventProperty from '../fields/FilterEventProperty.vue'
import FilterOperator from '../fields/FilterOperator.vue'
import FilterTimeInput from '../fields/FilterTimeInput.vue'
import FilterValue from '../fields/FilterValue.vue'
import FilterSingleWrapper from '../filter/FilterSingleWrapper.vue'

export default {
  name: 'FilterSingleDone',
  components: {
    BaseFilter,
    FilterSingleWrapper,
    FilterEventFieldSelect,
    FilterOperator,
    FilterValue,
    FilterEventFunction,
    FilterEventAction,
    FilterEventProperty,
    FilterTimeInput,
  },
  mixins: [validationMixin],
  inject: {
    filterContext: 'filterContext',
    filterListGroup: {
      from: 'filterListGroup',
      default: () => null,
    },
    // 从 ActionCollection 注入数据（可选）
    actionCollectionData: {
      from: 'actionCollectionData',
      default: () => null,
    },
    isActionCollectionComponent: {
      from: 'isActionCollectionComponent',
      default: () => false,
    },
    actionCollectionContext: {
      from: 'actionCollectionContext',
      default: () => null,
    },
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    onDelete: {
      type: Function,
      default: () => {},
    },
    onAdd: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      EVENT_ACTION: FILTER_CONFIG.EVENT_ACTION,
      _,
      MAX_FILTER_CONDITIONS: EVENT_CONSTANTS.MAX_FILTER_CONDITIONS,
    }
  },
  computed: {
    context() {
      return this.filterContext()
    },
    mode() {
      return this.context.mode || 'edit'
    },
    dataProvider() {
      return this.context.dataProvider || {}
    },
    isActionCollection() {
      return this.context.isActionCollection || false
    },
    showPushData() {
      return this.context.showPushData || false
    },
    // 安全地获取完整的过滤组数据
    fullFilterListGroup() {
      return this.filterListGroup ? this.filterListGroup() : null
    },
    eventAggregateProperty() {
      return this.value.eventAggregateProperty || {}
    },
    action() {
      return this.value.action
    },
    eventInfo() {
      return this.value.eventInfo || {}
    },
    dateRange() {
      return this.value.dateRange || []
    },
    eventFilterProperty() {
      return this.value.eventFilterProperty || {}
    },
    dataProviderComputed() {
      const pickPd = _.pick(this.dataProvider, 'getPropertyList')
      pickPd.eventId = this.eventInfo.id || 0
      return pickPd
    },
    funValue() {
      return getFunValue(this.eventAggregateProperty, FILTER_CONFIG)
    },
    eventTimeDisplayText() {
      return generateEventTimeDisplayText(this.value, FILTER_CONFIG)
    },
  },
  methods: {
    /**
     * 处理过滤器变更
     * @param {object} data - 过滤器数据
     */
    onChangeFilter(data) {
      console.log('🚀 ~ onChangeFilter ~ data:', JSON.parse(JSON.stringify(data)))
      this.value.changePropertyValue(data)
      this.onChange(this.value)
    },

    /**
     * 添加过滤条件
     */
    onAddFilter() {
      if (this.$refs.filterRef && this.$refs.filterRef.addFilterGroup) {
        const filters = this.$refs.filterRef.addFilterGroup()
        this.value.changePropertyValue(filters)
        this.onChange(this.value)
      }
    },

    /**
     * 处理 radio-group 的 change 事件
     */
    onPushDataChange(e) {
      this.handlePushDataChange(e.target.value)
    },

    /**
     * 处理 radio 的 click 事件（用于取消选择）
     */
    onRadioClick() {
      // 如果当前已经选中，则取消选择
      if (this.value.pushData === true) {
        this.handlePushDataChange(false)
      }
    },

    /**
     * 统一的 pushData 处理方法
     */
    handlePushDataChange(pushDataValue) {
      const isActionCollection = this.isActionCollectionComponent && this.isActionCollectionComponent()
      const actionCollectionContext = this.actionCollectionContext && this.actionCollectionContext()

      if (isActionCollection && actionCollectionContext && pushDataValue) {
        console.warn('使用 ActionCollection context 方法')
        FilterModelUtil.setPushDataExclusive(this.fullFilterListGroup, this.value, pushDataValue)
        actionCollectionContext.setActionCollectionPushDataExclusive(this.value)
        this.onChange(this.value)
      }
      else if (isActionCollection && !pushDataValue) {
        // 如果是取消选择，只设置自己为 false
        this.value.changePushData(false)
        this.onChange(this.value)
      }
      else {
        // 默认方法
        FilterModelUtil.setPushDataExclusive(this.fullFilterListGroup, this.value, pushDataValue)
        this.onChange(this.value)
      }
    },
  },
}
</script>

<template>
  <li :class="`FilterSingle ${mode}`">
    <div
      style="display: flex"
      :style="{
        display: mode !== 'edit' && !value.valid().isValid ? 'none' : 'flex',
      }"
    >
      <!-- 实时行为最开始的时间 -->
      <div :class="`FilterField ${mode} ${validator?.firstAction && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper
          :value="eventTimeDisplayText"
          :use-take-place-width="true"
          style="width:auto"
        >
          <FilterTimeInput :value="value" :on-change="onChange" type="first" />
        </FilterSingleWrapper>
      </div>

      <!-- 事件行为 -->
      <div :class="`FilterEventAction ${mode} ${validator?.action && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="EVENT_ACTION[action]" :use-take-place-width="true" style="width:auto">
          <FilterEventAction :value="value" :on-change="onChange" />
        </FilterSingleWrapper>
      </div>

      <!-- 事件选择 -->
      <div :class="`FilterField ${mode} ${validator?.id && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="eventInfo.displayName" :use-take-place-width="true" :style="mode === 'edit' ? 'min-width: 160px;' : ''">
          <FilterEventFieldSelect :value="value" :on-change="onChange" />
        </FilterSingleWrapper>
      </div>

      <!-- 事件属性 -->
      <div :class="`FilterField ${mode} ${validator?.eventAggregateProperty && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper
          :value="eventAggregateProperty.propertyType === 'TIMES' ? '次数' : eventAggregateProperty.property?.fieldName"
          :use-take-place-width="true"
        >
          <FilterEventProperty :value="value" :on-change="onChange" />
        </FilterSingleWrapper>
      </div>

      <!-- 计数函数 -->
      <div
        v-if="eventAggregateProperty.propertyType === 'EVENT_PROPERTY'"
        :class="`FilterField ${mode} ${validator?.fun && value.validating ? 'has-error' : ''}`"
      >
        <FilterSingleWrapper :value="funValue" :use-take-place-width="true">
          <FilterEventFunction :value="value" :on-change="onChange" />
        </FilterSingleWrapper>
      </div>

      <!-- 操作符 -->
      <div :class="`FilterOperator ${mode} ${validator?.operator && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="value.getOperatorShow()" :use-take-place-width="true">
          <FilterOperator :value="value" :on-change="onChange" />
        </FilterSingleWrapper>
      </div>

      <!-- 值 -->
      <div
        :class="`FilterValue ${mode} ${validator?.value && value.validating ? 'has-error' : ''}`"
        :style="{ display: value.isValueCanEdit() === false ? 'none' : 'block' }"
      >
        <FilterSingleWrapper :value="value.getValueShow()">
          <FilterValue :value="value" :on-change="onChange" />
        </FilterSingleWrapper>
      </div>

      <!-- 控制器 -->
      <div v-if="mode === 'edit'" class="Ctroller">
        <a-tooltip v-if="value.validating && hasValidationError" placement="topRight" :title="validationMessage">
          <div style="margin-right: 5px; color: #fa7777;">
            <a-icon type="question-circle" />
          </div>
        </a-tooltip>
        <span
          class="handleAdd"
          :style="{
            display:
              $refs.filterRef && $refs.filterRef.getFilterCount && $refs.filterRef.getFilterCount() >= MAX_FILTER_CONDITIONS
                ? 'none'
                : 'inline-block',
          }"
          @click="onAddFilter"
        >
          <a-icon type="plus-circle" /> <span class="add-text">条件</span>
        </span>
        <a-radio-group v-if="showPushData" :value="value.pushData" @change="onPushDataChange">
          <a-radio :value="true" @click="onRadioClick">
            <span class="checkboxTitle">推送数据</span>
          </a-radio>
        </a-radio-group>
        <a-icon type="close-circle" class="h-16" @click="onDelete" />
      </div>
    </div>
    <!-- 内部过滤器 -->
    <BaseFilter
      ref="filterRef"
      class="innerFilter"
      :data-provider="dataProviderComputed"
      :value="eventFilterProperty"
      :on-change="onChangeFilter"
      :mode="mode"
      :hide-add="true"
      :hide-init="true"
      add-button-text="添加过滤条件"
    />
  </li>
</template>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
